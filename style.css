* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #333;
}

.game-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    max-width: 800px;
    width: 100%;
}

.game-header {
    text-align: center;
    margin-bottom: 20px;
}

.game-header h1 {
    font-size: 2.5em;
    color: #4a5568;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.game-info {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.score-container, .level-container, .lines-container {
    text-align: center;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    padding: 10px 20px;
    border-radius: 10px;
    color: white;
    min-width: 100px;
}

.score-label, .level-label, .lines-label {
    font-size: 0.9em;
    opacity: 0.9;
}

.score, .level, .lines {
    font-size: 1.5em;
    font-weight: bold;
    margin-top: 5px;
}

.game-main {
    display: flex;
    gap: 20px;
    justify-content: center;
    align-items: flex-start;
    flex-wrap: wrap;
}

.game-board-container {
    position: relative;
    background: #2d3748;
    border-radius: 10px;
    padding: 10px;
    box-shadow: inset 0 0 20px rgba(0, 0, 0, 0.3);
}

#gameBoard {
    display: block;
    border: 2px solid #4a5568;
    border-radius: 5px;
    background: #1a202c;
}

.game-over {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    z-index: 100;
}

.game-over-text {
    font-size: 2em;
    margin-bottom: 20px;
    color: #ff6b6b;
}

.restart-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 1.1em;
    cursor: pointer;
    transition: transform 0.2s;
}

.restart-btn:hover {
    transform: scale(1.05);
}

.game-sidebar {
    display: flex;
    flex-direction: column;
    gap: 20px;
    min-width: 200px;
}

.next-piece-container {
    background: #f7fafc;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    border: 2px solid #e2e8f0;
}

.next-label {
    font-weight: bold;
    margin-bottom: 10px;
    color: #4a5568;
}

#nextPiece {
    border: 1px solid #cbd5e0;
    border-radius: 5px;
    background: white;
}

.controls {
    background: #f7fafc;
    border-radius: 10px;
    padding: 15px;
    border: 2px solid #e2e8f0;
}

.controls h3 {
    margin-bottom: 15px;
    color: #4a5568;
    text-align: center;
}

.control-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding: 5px 0;
}

.key {
    background: #4a5568;
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.9em;
    font-weight: bold;
    min-width: 50px;
    text-align: center;
}

.desc {
    font-size: 0.9em;
    color: #666;
}

.game-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.game-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 25px;
    font-size: 1em;
    cursor: pointer;
    transition: transform 0.2s;
    font-weight: bold;
}

.game-btn:hover {
    transform: scale(1.05);
}

.game-btn:active {
    transform: scale(0.95);
}

@media (max-width: 768px) {
    .game-container {
        margin: 10px;
        padding: 15px;
    }
    
    .game-main {
        flex-direction: column;
        align-items: center;
    }
    
    .game-info {
        gap: 15px;
    }
    
    .score-container, .level-container, .lines-container {
        min-width: 80px;
        padding: 8px 15px;
    }
    
    #gameBoard {
        width: 250px;
        height: 500px;
    }
}
