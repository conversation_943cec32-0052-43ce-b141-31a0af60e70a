<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <h1>俄罗斯方块</h1>
            <div class="game-info">
                <div class="score-container">
                    <div class="score-label">得分</div>
                    <div class="score" id="score">0</div>
                </div>
                <div class="level-container">
                    <div class="level-label">等级</div>
                    <div class="level" id="level">1</div>
                </div>
                <div class="lines-container">
                    <div class="lines-label">消除行数</div>
                    <div class="lines" id="lines">0</div>
                </div>
            </div>
        </div>
        
        <div class="game-main">
            <div class="game-board-container">
                <canvas id="gameBoard" width="300" height="600"></canvas>
                <div class="game-over" id="gameOver" style="display: none;">
                    <div class="game-over-text">游戏结束</div>
                    <button class="restart-btn" onclick="restartGame()">重新开始</button>
                </div>
            </div>
            
            <div class="game-sidebar">
                <div class="next-piece-container">
                    <div class="next-label">下一个</div>
                    <canvas id="nextPiece" width="120" height="120"></canvas>
                </div>
                
                <div class="controls">
                    <h3>控制说明</h3>
                    <div class="control-item">
                        <span class="key">←→</span>
                        <span class="desc">左右移动</span>
                    </div>
                    <div class="control-item">
                        <span class="key">↓</span>
                        <span class="desc">快速下降</span>
                    </div>
                    <div class="control-item">
                        <span class="key">↑</span>
                        <span class="desc">旋转</span>
                    </div>
                    <div class="control-item">
                        <span class="key">空格</span>
                        <span class="desc">暂停/继续</span>
                    </div>
                </div>
                
                <div class="game-buttons">
                    <button class="game-btn" onclick="pauseGame()" id="pauseBtn">暂停</button>
                    <button class="game-btn" onclick="restartGame()">重新开始</button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
