// 游戏配置
const BOARD_WIDTH = 10;
const BOARD_HEIGHT = 20;
const CELL_SIZE = 30;

// 游戏状态
let board = [];
let currentPiece = null;
let nextPiece = null;
let score = 0;
let level = 1;
let lines = 0;
let gameRunning = false;
let gamePaused = false;
let dropTime = 0;
let lastTime = 0;

// Canvas 元素
const canvas = document.getElementById('gameBoard');
const ctx = canvas.getContext('2d');
const nextCanvas = document.getElementById('nextPiece');
const nextCtx = nextCanvas.getContext('2d');

// 方块形状定义
const PIECES = {
    I: {
        shape: [
            [0, 0, 0, 0],
            [1, 1, 1, 1],
            [0, 0, 0, 0],
            [0, 0, 0, 0]
        ],
        color: '#00f5ff'
    },
    O: {
        shape: [
            [1, 1],
            [1, 1]
        ],
        color: '#ffff00'
    },
    T: {
        shape: [
            [0, 1, 0],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#a000f0'
    },
    S: {
        shape: [
            [0, 1, 1],
            [1, 1, 0],
            [0, 0, 0]
        ],
        color: '#00f000'
    },
    Z: {
        shape: [
            [1, 1, 0],
            [0, 1, 1],
            [0, 0, 0]
        ],
        color: '#f00000'
    },
    J: {
        shape: [
            [1, 0, 0],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#0000f0'
    },
    L: {
        shape: [
            [0, 0, 1],
            [1, 1, 1],
            [0, 0, 0]
        ],
        color: '#ff7f00'
    }
};

// 初始化游戏板
function initBoard() {
    board = [];
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        board[y] = [];
        for (let x = 0; x < BOARD_WIDTH; x++) {
            board[y][x] = 0;
        }
    }
}

// 创建新方块
function createPiece() {
    const pieces = Object.keys(PIECES);
    const randomPiece = pieces[Math.floor(Math.random() * pieces.length)];
    return {
        type: randomPiece,
        shape: PIECES[randomPiece].shape,
        color: PIECES[randomPiece].color,
        x: Math.floor(BOARD_WIDTH / 2) - Math.floor(PIECES[randomPiece].shape[0].length / 2),
        y: 0
    };
}

// 绘制单个方块
function drawCell(ctx, x, y, color) {
    ctx.fillStyle = color;
    ctx.fillRect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.strokeRect(x * CELL_SIZE, y * CELL_SIZE, CELL_SIZE, CELL_SIZE);
}

// 绘制游戏板
function drawBoard() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制已放置的方块
    for (let y = 0; y < BOARD_HEIGHT; y++) {
        for (let x = 0; x < BOARD_WIDTH; x++) {
            if (board[y][x]) {
                drawCell(ctx, x, y, board[y][x]);
            }
        }
    }

    // 绘制当前方块
    if (currentPiece) {
        drawPiece(ctx, currentPiece);
    }
}

// 绘制方块
function drawPiece(context, piece) {
    for (let y = 0; y < piece.shape.length; y++) {
        for (let x = 0; x < piece.shape[y].length; x++) {
            if (piece.shape[y][x]) {
                drawCell(context, piece.x + x, piece.y + y, piece.color);
            }
        }
    }
}

// 检查碰撞
function checkCollision(piece, dx = 0, dy = 0, newShape = null) {
    const shape = newShape || piece.shape;
    const newX = piece.x + dx;
    const newY = piece.y + dy;

    for (let y = 0; y < shape.length; y++) {
        for (let x = 0; x < shape[y].length; x++) {
            if (shape[y][x]) {
                const boardX = newX + x;
                const boardY = newY + y;

                if (boardX < 0 || boardX >= BOARD_WIDTH ||
                    boardY >= BOARD_HEIGHT ||
                    (boardY >= 0 && board[boardY][boardX])) {
                    return true;
                }
            }
        }
    }
    return false;
}

// 旋转方块
function rotatePiece(piece) {
    const rotated = [];
    const size = piece.shape.length;

    for (let i = 0; i < size; i++) {
        rotated[i] = [];
        for (let j = 0; j < size; j++) {
            rotated[i][j] = piece.shape[size - 1 - j][i];
        }
    }

    return rotated;
}

// 放置方块到游戏板
function placePiece() {
    for (let y = 0; y < currentPiece.shape.length; y++) {
        for (let x = 0; x < currentPiece.shape[y].length; x++) {
            if (currentPiece.shape[y][x]) {
                const boardY = currentPiece.y + y;
                const boardX = currentPiece.x + x;
                if (boardY >= 0) {
                    board[boardY][boardX] = currentPiece.color;
                }
            }
        }
    }
}

// 检查并清除完整的行
function clearLines() {
    let linesCleared = 0;

    for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
        if (board[y].every(cell => cell !== 0)) {
            board.splice(y, 1);
            board.unshift(new Array(BOARD_WIDTH).fill(0));
            linesCleared++;
            y++; // 重新检查这一行
        }
    }

    if (linesCleared > 0) {
        lines += linesCleared;
        score += linesCleared * 100 * level;
        level = Math.floor(lines / 10) + 1;
        updateDisplay();
    }
}

// 更新显示
function updateDisplay() {
    document.getElementById('score').textContent = score;
    document.getElementById('level').textContent = level;
    document.getElementById('lines').textContent = lines;
}

// 绘制下一个方块
function drawNextPiece() {
    nextCtx.clearRect(0, 0, nextCanvas.width, nextCanvas.height);
    if (nextPiece) {
        const offsetX = (nextCanvas.width - nextPiece.shape[0].length * 20) / 2;
        const offsetY = (nextCanvas.height - nextPiece.shape.length * 20) / 2;

        for (let y = 0; y < nextPiece.shape.length; y++) {
            for (let x = 0; x < nextPiece.shape[y].length; x++) {
                if (nextPiece.shape[y][x]) {
                    nextCtx.fillStyle = nextPiece.color;
                    nextCtx.fillRect(offsetX + x * 20, offsetY + y * 20, 20, 20);
                    nextCtx.strokeStyle = '#333';
                    nextCtx.lineWidth = 1;
                    nextCtx.strokeRect(offsetX + x * 20, offsetY + y * 20, 20, 20);
                }
            }
        }
    }
}

// 游戏结束检查
function checkGameOver() {
    return checkCollision(currentPiece);
}

// 移动方块
function movePiece(dx, dy) {
    if (!checkCollision(currentPiece, dx, dy)) {
        currentPiece.x += dx;
        currentPiece.y += dy;
        return true;
    }
    return false;
}

// 旋转当前方块
function rotateCurrentPiece() {
    const rotated = rotatePiece(currentPiece);
    if (!checkCollision(currentPiece, 0, 0, rotated)) {
        currentPiece.shape = rotated;
    }
}

// 硬降落
function hardDrop() {
    while (movePiece(0, 1)) {
        score += 2;
    }
    updateDisplay();
}

// 生成新方块
function spawnPiece() {
    currentPiece = nextPiece || createPiece();
    nextPiece = createPiece();
    drawNextPiece();

    if (checkGameOver()) {
        gameOver();
        return false;
    }
    return true;
}

// 游戏主循环
function gameLoop(time = 0) {
    if (!gameRunning || gamePaused) {
        if (gameRunning) {
            requestAnimationFrame(gameLoop);
        }
        return;
    }

    const deltaTime = time - lastTime;
    lastTime = time;
    dropTime += deltaTime;

    const dropInterval = Math.max(50, 1000 - (level - 1) * 100);

    if (dropTime > dropInterval) {
        if (!movePiece(0, 1)) {
            placePiece();
            clearLines();
            if (!spawnPiece()) {
                return;
            }
        }
        dropTime = 0;
    }

    drawBoard();
    requestAnimationFrame(gameLoop);
}

// 开始游戏
function startGame() {
    initBoard();
    score = 0;
    level = 1;
    lines = 0;
    gameRunning = true;
    gamePaused = false;
    dropTime = 0;
    lastTime = 0;

    nextPiece = createPiece();
    spawnPiece();
    updateDisplay();

    document.getElementById('gameOver').style.display = 'none';
    document.getElementById('pauseBtn').textContent = '暂停';

    requestAnimationFrame(gameLoop);
}

// 暂停游戏
function pauseGame() {
    if (!gameRunning) return;

    gamePaused = !gamePaused;
    document.getElementById('pauseBtn').textContent = gamePaused ? '继续' : '暂停';

    if (!gamePaused) {
        requestAnimationFrame(gameLoop);
    }
}

// 游戏结束
function gameOver() {
    gameRunning = false;
    document.getElementById('gameOver').style.display = 'block';
}

// 重新开始游戏
function restartGame() {
    startGame();
}

// 键盘控制
document.addEventListener('keydown', (event) => {
    if (!gameRunning || gamePaused) {
        if (event.code === 'Space') {
            pauseGame();
        }
        return;
    }

    switch (event.code) {
        case 'ArrowLeft':
            movePiece(-1, 0);
            break;
        case 'ArrowRight':
            movePiece(1, 0);
            break;
        case 'ArrowDown':
            if (movePiece(0, 1)) {
                score += 1;
                updateDisplay();
            }
            break;
        case 'ArrowUp':
            rotateCurrentPiece();
            break;
        case 'Space':
            pauseGame();
            break;
    }

    drawBoard();
    event.preventDefault();
});

// 初始化游戏
window.addEventListener('load', () => {
    startGame();
});
